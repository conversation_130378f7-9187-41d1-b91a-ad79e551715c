// EditAction model
enum EditType {
  clipSplit,
  clipDelete,
  clipCrop,
  clipRotate,
  clipFlip,
  filterChange,
  effectChange,
  textAdd,
  textEdit,
  textDelete,
  transitionAdd,
  transitionDelete,
  audioAdjust,
}

class EditAction {
  final EditType type;
  final dynamic previousValue;
  final dynamic newValue;
  final DateTime timestamp;

  EditAction({
    required this.type,
    required this.previousValue,
    required this.newValue,
  }) : timestamp = DateTime.now();

  // Create a copy of this edit action with new values
  EditAction copyWith({
    EditType? type,
    dynamic previousValue,
    dynamic newValue,
  }) {
    return EditAction(
      type: type ?? this.type,
      previousValue: previousValue ?? this.previousValue,
      newValue: newValue ?? this.newValue,
    );
  }

  // Check if this edit action is the same as another object
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is EditAction &&
        other.type == type &&
        other.timestamp == timestamp;
  }

  @override
  int get hashCode => type.hashCode ^ timestamp.hashCode;
}
