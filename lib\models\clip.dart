import 'package:flutter/material.dart';

// Clip model
class Clip {
  double start;
  double end;
  String path;
  Color color;

  Clip({
    required this.start,
    required this.end,
    required this.path,
    required this.color,
  });

  // Create a new clip from a file
  factory Clip.fromFile(
    String path, {
    double start = 0,
    double end = 5,
    Color? color,
  }) {
    // In a real app, this would get the actual video duration
    // For this prototype, we'll assume a default duration of 5s
    return Clip(
      start: start,
      end: end,
      path: path,
      color: color ?? Colors.blue[300]!,
    );
  }

  // Create a copy of this clip with new values
  Clip copyWith({double? start, double? end, String? path, Color? color}) {
    return Clip(
      start: start ?? this.start,
      end: end ?? this.end,
      path: path ?? this.path,
      color: color ?? this.color,
    );
  }

  // Get the duration of the clip
  Duration get duration => Duration(seconds: (end - start).toInt());

  // Get the file name from the path
  String get fileName => path.split('/').last;

  // Check if this clip is the same as another object
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is Clip &&
        other.start == start &&
        other.end == end &&
        other.path == path;
  }

  @override
  int get hashCode => path.hashCode ^ start.hashCode ^ end.hashCode;
}
