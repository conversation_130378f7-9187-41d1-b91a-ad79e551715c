import 'package:flutter/material.dart';

// Transition model
enum TransitionType {
  fade,
  slide,
  zoom,
  rotate,
  flip,
  crossFade,
  dissolve,
  wipe,
  push,
  cube,
  door,
}

class Transition {
  final TransitionType type;
  final String name;
  final IconData icon;
  final double duration;
  final Rect position;
  final Color color;

  Transition({
    required this.type,
    this.duration = 1.0,
    this.position = const Rect.fromLTWH(0, 0, 0, 0),
    required this.color,
  }) : name = _getTransitionName(type),
       icon = _getTransitionIcon(type);

  // Get transition name based on type
  static String _getTransitionName(TransitionType type) {
    switch (type) {
      case TransitionType.fade:
        return 'Fade';
      case TransitionType.slide:
        return 'Slide';
      case TransitionType.zoom:
        return 'Zoom';
      case TransitionType.rotate:
        return 'Rotate';
      case TransitionType.flip:
        return 'Flip';
      case TransitionType.crossFade:
        return 'Cross Fade';
      case TransitionType.dissolve:
        return 'Dissolve';
      case TransitionType.wipe:
        return 'Wipe';
      case TransitionType.push:
        return 'Push';
      case TransitionType.cube:
        return 'Cube';
      case TransitionType.door:
        return 'Door';
    }
  }

  // Get transition icon based on type
  static IconData _getTransitionIcon(TransitionType type) {
    switch (type) {
      case TransitionType.fade:
        return Icons.opacity;
      case TransitionType.slide:
        return Icons.arrow_right_alt;
      case TransitionType.zoom:
        return Icons.zoom_out;
      case TransitionType.rotate:
        return Icons.rotate_right;
      case TransitionType.flip:
        return Icons.flip;
      case TransitionType.crossFade:
        return Icons.compare_arrows;
      case TransitionType.dissolve:
        return Icons.blur_on;
      case TransitionType.wipe:
        return Icons.arrow_right_alt;
      case TransitionType.push:
        return Icons.arrow_forward;
      case TransitionType.cube:
        return Icons.layers;
      case TransitionType.door:
        return Icons.door_back_door;
    }
  }

  // Create a copy of this transition with new values
  Transition copyWith({
    TransitionType? type,
    double? duration,
    Rect? position,
    Color? color,
  }) {
    return Transition(
      type: type ?? this.type,
      duration: duration ?? this.duration,
      position: position ?? this.position,
      color: color ?? this.color,
    );
  }

  // Check if this transition is the same as another object
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is Transition && other.type == type;
  }

  @override
  int get hashCode => type.hashCode;
}
