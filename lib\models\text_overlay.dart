import 'package:flutter/material.dart';

// TextOverlay model
class TextOverlay {
  int index;
  String text;
  double fontSize;
  Color color;
  TextAlign align;
  FontStyle style;
  FontWeight weight;

  TextOverlay({
    required this.index,
    required this.text,
    this.fontSize = 24.0,
    this.color = Colors.white,
    this.align = TextAlign.center,
    this.style = FontStyle.normal,
    this.weight = FontWeight.normal,
  });

  // Create a copy of this text overlay with new values
  TextOverlay copyWith({
    int? index,
    String? text,
    double? fontSize,
    Color? color,
    TextAlign? align,
    FontStyle? style,
    FontWeight? weight,
  }) {
    return TextOverlay(
      index: index ?? this.index,
      text: text ?? this.text,
      fontSize: fontSize ?? this.fontSize,
      color: color ?? this.color,
      align: align ?? this.align,
      style: style ?? this.style,
      weight: weight ?? this.weight,
    );
  }

  // Check if this text overlay is the same as another object
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is TextOverlay && other.index == index;
  }

  @override
  int get hashCode => index.hashCode;
}
