// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:montagepro/main.dart';

void main() {
  testWidgets('Video editor app smoke test', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const VideoEditorApp());

    // Verify that the app loads with the correct title.
    expect(find.text('MontagePro'), findsOneWidget);

    // Verify that the preview area is present.
    expect(find.text('Preview Area'), findsOneWidget);

    // Verify that the timeline is present.
    expect(find.text('Timeline'), findsOneWidget);

    // Verify that the floating action button is present.
    expect(find.byIcon(Icons.add), findsOneWidget);
  });
}
