import 'package:flutter/material.dart';
import 'clip.dart';
import 'audio_track.dart';
import 'text_overlay.dart';
import 'transition.dart';
import 'edit_action.dart';

// Project model
class Project {
  String id;
  String name;
  String path;
  DateTime createdAt;
  DateTime lastModified;
  List<Clip> clips;
  List<AudioTrack> audioTracks;
  List<TextOverlay> textOverlays;
  List<Transition> transitions;
  List<EditAction> editHistory;
  BuildContext? context; // Reference to the current context for UI operations

  Project({
    required this.id,
    required this.name,
    required this.path,
    required this.createdAt,
    required this.lastModified,
    required this.clips,
    required this.audioTracks,
    required this.textOverlays,
    required this.transitions,
    required this.editHistory,
    this.context,
  });

  // Create a new project with default values
  factory Project.empty({required String name, required BuildContext context}) {
    final now = DateTime.now();
    return Project(
      id: 'project_${now.millisecondsSinceEpoch}',
      name: name,
      path: '',
      createdAt: now,
      lastModified: now,
      clips: [],
      audioTracks: [
        AudioTrack(
          id: now.millisecondsSinceEpoch,
          name: 'Audio Track 1',
          volume: 1.0,
          mute: false,
          solo: false,
          color: Colors.primaries[0],
        ),
      ],
      textOverlays: [],
      transitions: [],
      editHistory: [],
      context: context,
    );
  }

  // Create a copy of this project with new values
  Project copyWith({
    String? id,
    String? name,
    String? path,
    DateTime? createdAt,
    DateTime? lastModified,
    List<Clip>? clips,
    List<AudioTrack>? audioTracks,
    List<TextOverlay>? textOverlays,
    List<Transition>? transitions,
    List<EditAction>? editHistory,
    BuildContext? context,
  }) {
    return Project(
      id: id ?? this.id,
      name: name ?? this.name,
      path: path ?? this.path,
      createdAt: createdAt ?? this.createdAt,
      lastModified: lastModified ?? this.lastModified,
      clips: clips ?? this.clips,
      audioTracks: audioTracks ?? this.audioTracks,
      textOverlays: textOverlays ?? this.textOverlays,
      transitions: transitions ?? this.transitions,
      editHistory: editHistory ?? this.editHistory,
      context: context ?? this.context,
    );
  }

  // Get the duration of the project
  Duration get duration {
    if (clips.isEmpty) return Duration.zero;
    return Duration(
      seconds:
          clips.map((clip) => clip.end).reduce((a, b) => a > b ? a : b).toInt(),
    );
  }

  // Add a clip to the project
  void addClip(Clip clip) {
    clips.add(clip);
    lastModified = DateTime.now();
  }

  // Remove a clip from the project
  void removeClip(Clip clip) {
    clips.remove(clip);
    lastModified = DateTime.now();
  }

  // Add an audio track to the project
  void addAudioTrack(AudioTrack track) {
    audioTracks.add(track);
    lastModified = DateTime.now();
  }

  // Remove an audio track from the project
  void removeAudioTrack(AudioTrack track) {
    audioTracks.remove(track);
    lastModified = DateTime.now();
  }

  // Add a text overlay to the project
  void addTextOverlay(TextOverlay overlay) {
    textOverlays.add(overlay);
    lastModified = DateTime.now();
  }

  // Remove a text overlay from the project
  void removeTextOverlay(TextOverlay overlay) {
    textOverlays.remove(overlay);
    lastModified = DateTime.now();
  }

  // Add a transition to the project
  void addTransition(Transition transition) {
    transitions.add(transition);
    lastModified = DateTime.now();
  }

  // Remove a transition from the project
  void removeTransition(Transition transition) {
    transitions.remove(transition);
    lastModified = DateTime.now();
  }

  // Record an edit in the history
  void recordEdit(EditAction action) {
    editHistory.add(action);
    lastModified = DateTime.now();
  }

  // Clear the edit history
  void clearHistory() {
    editHistory.clear();
    lastModified = DateTime.now();
  }

  // Get the project's size
  int get size {
    // In a real app, this would calculate the actual size of all files
    // For this prototype, we'll return a simulated value
    return clips.length * 100 +
        audioTracks.length * 10 +
        textOverlays.length * 5;
  }

  // Get the number of media files in the project
  int get mediaFileCount {
    return clips.length + audioTracks.length;
  }

  // Check if the project has unsaved changes
  bool get hasUnsavedChanges {
    // In a real app, this would compare the current state with the last saved state
    // For this prototype, we'll just check if there are any edit actions
    return editHistory.isNotEmpty;
  }

  // Get the list of media files used in the project
  List<String> get mediaFiles {
    return clips.map((clip) => clip.path).toList();
  }

  // Get the list of media files used in the project
  List<String> get allMediaFiles {
    final files = <String>[];
    files.addAll(clips.map((clip) => clip.path));
    // For this prototype, we won't include audio files
    return files;
  }

  // Get the list of fonts used in the project
  List<String> get usedFonts {
    // In a real app, this would return all fonts used in text overlays
    // For this prototype, we'll just return a default font
    return ['Roboto'];
  }
}
