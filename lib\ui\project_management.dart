import 'package:flutter/material.dart';
import '../models/project.dart';

// Project list screen
class ProjectListScreen extends StatelessWidget {
  final List<Project> projects;
  final Function(Project) onProjectSelected;
  final Function() onNewProject;
  final Function() onRestoreAutoSave;

  const ProjectListScreen({
    super.key,
    required this.projects,
    required this.onProjectSelected,
    required this.onNewProject,
    required this.onRestoreAutoSave,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('MontagePro'),
        actions: [
          IconButton(
            icon: const Icon(Icons.settings_backup_restore),
            onPressed: onRestoreAutoSave,
            tooltip: 'Restore Auto-Saved Project',
          ),
        ],
      ),
      body:
          projects.isEmpty
              ? const Center(
                child: Text(
                  'No projects found. Tap the + button to create a new project.',
                ),
              )
              : ListView.builder(
                itemCount: projects.length,
                itemBuilder: (context, index) {
                  return _buildProjectItem(context, projects[index]);
                },
              ),
      floatingActionButton: FloatingActionButton(
        onPressed: onNewProject,
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildProjectItem(BuildContext context, Project project) {
    final duration = project.duration;
    final hours = duration.inHours;
    final minutes = duration.inMinutes % 60;
    final seconds = duration.inSeconds % 60;

    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
      child: ListTile(
        leading: const Icon(Icons.video_library),
        title: Text(project.name),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Last modified: ${_formatDate(project.lastModified)}'),
            Text(
              'Duration: ${hours > 0 ? '$hours:' : ''}${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}',
            ),
            Text('Media files: ${project.mediaFileCount}'),
          ],
        ),
        trailing: const Icon(Icons.chevron_right),
        onTap: () => onProjectSelected(project),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year} ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
  }
}

// New project screen
class NewProjectScreen extends StatefulWidget {
  final Function(Project) onProjectCreated;

  const NewProjectScreen({super.key, required this.onProjectCreated});

  @override
  State<NewProjectScreen> createState() => _NewProjectScreenState();
}

class _NewProjectScreenState extends State<NewProjectScreen> {
  final _formKey = GlobalKey<FormState>();
  late String _projectName;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('New Project')),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            children: [
              TextFormField(
                decoration: const InputDecoration(
                  labelText: 'Project Name',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a project name';
                  }
                  return null;
                },
                onSaved: (value) {
                  _projectName = value!;
                },
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () {
                  if (_formKey.currentState!.validate()) {
                    _formKey.currentState!.save();

                    final project = Project.empty(
                      name: _projectName,
                      context: context,
                    );
                    widget.onProjectCreated(project);
                  }
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange,
                  foregroundColor: Colors.black,
                ),
                child: const Text('Create Project'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// Project details screen
class ProjectDetailsScreen extends StatelessWidget {
  final Project project;
  final Function(Project) onSave;
  final Function(Project) onDelete;
  final Function(Project) onExport;

  const ProjectDetailsScreen({
    super.key,
    required this.project,
    required this.onSave,
    required this.onDelete,
    required this.onExport,
  });

  @override
  Widget build(BuildContext context) {
    final duration = project.duration;
    final hours = duration.inHours;
    final minutes = duration.inMinutes % 60;
    final seconds = duration.inSeconds % 60;

    return Scaffold(
      appBar: AppBar(
        title: Text(project.name),
        actions: [
          IconButton(
            icon: const Icon(Icons.delete),
            onPressed: () => _showDeleteDialog(context),
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Project ID: ${project.id}',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 8),
            Text(
              'Created: ${_formatDate(project.createdAt)}',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 8),
            Text(
              'Last Modified: ${_formatDate(project.lastModified)}',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 8),
            Text(
              'Duration: ${hours > 0 ? '$hours:' : ''}${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 8),
            Text(
              'Media files: ${project.mediaFileCount}',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => onSave(project),
                    icon: const Icon(Icons.save),
                    label: const Text('Save Project'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.black,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => onExport(project),
                    icon: const Icon(Icons.file_download),
                    label: const Text('Export Project'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _showDeleteDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('Delete Project'),
          content: const Text(
            'Are you sure you want to delete this project? This action cannot be undone.',
          ),
          actions: [
            TextButton(
              onPressed: Navigator.of(context).pop,
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                // In a real app, this would delete the project
                // For this prototype, we'll just simulate the operation
                Navigator.of(context).pop();
              },
              style: TextButton.styleFrom(foregroundColor: Colors.red),
              child: const Text('Delete'),
            ),
          ],
        );
      },
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year} ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
  }
}

// Project management panel
class ProjectManagementPanel extends StatelessWidget {
  final Project project;
  final Function() onSave;
  final Function() onAutoSaveSettings;
  final Function() onCloudBackup;
  final Function() onImportProject;
  final Function() onExportProject;

  const ProjectManagementPanel({
    super.key,
    required this.project,
    required this.onSave,
    required this.onAutoSaveSettings,
    required this.onCloudBackup,
    required this.onImportProject,
    required this.onExportProject,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.grey[900],
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Project Management',
                style: TextStyle(color: Colors.white, fontSize: 16),
              ),
              IconButton(
                icon: Icon(Icons.close, color: Colors.white),
                onPressed: () {
                  Navigator.of(context).pop();
                },
              ),
            ],
          ),
          // Current project name
          Row(
            children: [
              Icon(Icons.video_library, color: Colors.orange),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  project.name,
                  style: TextStyle(color: Colors.white, fontSize: 16),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          // Project actions
          _buildProjectAction('Save Project', Icons.save, onSave),
          const SizedBox(height: 8),
          _buildProjectAction(
            'Auto-Save Settings',
            Icons.backup,
            onAutoSaveSettings,
          ),
          const SizedBox(height: 8),
          _buildProjectAction(
            'Cloud Backup',
            Icons.cloud_upload,
            onCloudBackup,
          ),
          const SizedBox(height: 8),
          _buildProjectAction(
            'Import Project',
            Icons.file_download,
            onImportProject,
          ),
          const SizedBox(height: 8),
          _buildProjectAction(
            'Export Project',
            Icons.file_upload,
            onExportProject,
          ),
          const SizedBox(height: 16),
          // Project info
          Text(
            'Project Info',
            style: TextStyle(color: Colors.white, fontSize: 14),
          ),
          const SizedBox(height: 8),
          _buildInfoItem('Project ID', project.id),
          _buildInfoItem('Created', _formatDate(project.createdAt)),
          _buildInfoItem('Last Modified', _formatDate(project.lastModified)),
          _buildInfoItem('Size', '${project.size} MB'),
          _buildInfoItem('Media Files', '${project.mediaFileCount}'),
          _buildInfoItem('Text Overlays', '${project.textOverlays.length}'),
          _buildInfoItem(
            'Edit History',
            '${project.editHistory.length} actions',
          ),
        ],
      ),
    );
  }

  Widget _buildProjectAction(
    String label,
    IconData icon,
    VoidCallback onPressed,
  ) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, color: Colors.white),
      label: Text(label),
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.grey[800],
        foregroundColor: Colors.white,
        alignment: Alignment.centerLeft,
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      ),
    );
  }

  Widget _buildInfoItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        children: [
          Text('$label: ', style: TextStyle(color: Colors.grey[600])),
          Text(value, style: TextStyle(color: Colors.white)),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year} ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
  }
}
