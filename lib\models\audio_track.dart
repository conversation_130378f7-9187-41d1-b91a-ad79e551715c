import 'dart:ui';
import 'package:flutter/material.dart';

// AudioTrack model
class AudioTrack {
  final int id;
  String name;
  double volume;
  bool mute;
  bool solo;
  final Color color;

  AudioTrack({
    required this.id,
    required this.name,
    this.volume = 1.0,
    this.mute = false,
    this.solo = false,
    required this.color,
  });

  // Create a copy of this audio track with new values
  AudioTrack copyWith({String? name, double? volume, bool? mute, bool? solo}) {
    return AudioTrack(
      id: id,
      name: name ?? this.name,
      volume: volume ?? this.volume,
      mute: mute ?? this.mute,
      solo: solo ?? this.solo,
      color: color,
    );
  }

  // Check if this audio track is the same as another object
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is AudioTrack && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
