import 'dart:io';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';

// Import models
import '../models/project.dart';

class ProjectService {
  // Singleton pattern to have a single instance throughout the app
  static final ProjectService _instance = ProjectService._internal();

  factory ProjectService() {
    return _instance;
  }

  ProjectService._internal();

  // Get the directory where projects are saved
  Future<Directory> _getProjectsDirectory() async {
    final directory = await getApplicationDocumentsDirectory();
    final projectsDirectory = Directory(path.join(directory.path, 'projects'));

    if (!projectsDirectory.existsSync()) {
      projectsDirectory.createSync();
    }

    return projectsDirectory;
  }

  // Get the directory where temporary project files are saved
  Future<Directory> _getTempDirectory() async {
    final directory = await getTemporaryDirectory();
    return directory;
  }

  // Load all projects (alias for listProjects)
  Future<List<Project>> loadProjects() async {
    return listProjects();
  }

  // Create a new project
  Future<Project> createProject(String name) async {
    final projectsDirectory = await _getProjectsDirectory();
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final projectDirectory = Directory(
      path.join(projectsDirectory.path, 'project_$timestamp'),
    );

    if (!projectDirectory.existsSync()) {
      projectDirectory.createSync();
    }

    return Project(
      id: 'project_$timestamp',
      name: name,
      path: projectDirectory.path,
      createdAt: DateTime.now(),
      lastModified: DateTime.now(),
      clips: [],
      audioTracks: [],
      textOverlays: [],
      transitions: [],
      editHistory: [],
    );
  }

  // Save a project
  Future<void> saveProject(Project project) async {
    // In a real app, we would serialize the project to JSON and save it
    // For this prototype, we'll just update the last modified time
    project.lastModified = DateTime.now();

    // Save project metadata
    // In a real app, we would save the project structure to disk
    // using File('$path/project.json').writeAsString(jsonEncode(project));
  }

  // Load a project
  Future<Project> loadProject(String projectId) async {
    // In a real app, we would load the project from disk
    // For this prototype, we'll create a dummy project
    final projectsDirectory = await _getProjectsDirectory();
    final projectDirectory = Directory(
      path.join(projectsDirectory.path, projectId),
    );

    if (!projectDirectory.existsSync()) {
      throw Exception('Project not found');
    }

    return Project(
      id: projectId,
      name: 'Project $projectId',
      path: projectDirectory.path,
      createdAt: DateTime.now().subtract(Duration(days: 1)),
      lastModified: DateTime.now(),
      clips: [],
      audioTracks: [],
      textOverlays: [],
      transitions: [],
      editHistory: [],
    );
  }

  // List all saved projects
  Future<List<Project>> listProjects() async {
    final projectsDirectory = await _getProjectsDirectory();

    if (!projectsDirectory.existsSync()) {
      return [];
    }

    // In a real app, we would parse project metadata
    // For this prototype, we'll return dummy projects
    final projects = <Project>[];

    // Get all project directories
    final directories = projectsDirectory.listSync().whereType<Directory>();

    for (final directory in directories) {
      projects.add(
        Project(
          id: path.basename(directory.path),
          name: 'Project ${path.basename(directory.path)}',
          path: directory.path,
          createdAt: directory.statSync().modified,
          lastModified: directory.statSync().modified,
          clips: [],
          audioTracks: [],
          textOverlays: [],
          transitions: [],
          editHistory: [],
        ),
      );
    }

    // Sort projects by last modified
    projects.sort((a, b) => b.lastModified.compareTo(a.lastModified));

    return projects;
  }

  // Delete a project
  Future<void> deleteProject(String projectId) async {
    final projectsDirectory = await _getProjectsDirectory();
    final projectDirectory = Directory(
      path.join(projectsDirectory.path, projectId),
    );

    if (projectDirectory.existsSync()) {
      projectDirectory.deleteSync(recursive: true);
    }
  }

  // Save a project snapshot
  Future<String> saveProjectSnapshot(
    Project project, {
    bool autoSave = false,
  }) async {
    final projectName = autoSave ? '${project.id}_autosave' : project.id;
    final projectPath = path.join(
      (await _getProjectsDirectory()).path,
      projectName,
    );
    final projectDirectory = Directory(projectPath);

    // Create project directory if it doesn't exist
    if (!projectDirectory.existsSync()) {
      projectDirectory.createSync();
    }

    // In a real app, we would save the project structure to disk
    // For this prototype, we'll just simulate the operation

    // Save project metadata
    // File('$projectPath/project.json').writeAsString(jsonEncode(project));

    // Save media files
    for (final _ in project.clips) {
      // In a real app, we would copy the media file to the project directory
      // For this prototype, we'll just simulate the operation
    }

    // Save edit history
    if (project.editHistory.isNotEmpty) {
      // File('$projectPath/history.json').writeAsString(jsonEncode(project.editHistory));
    }

    // Update project last modified time
    project.lastModified = DateTime.now();

    return projectPath;
  }

  // Restore a project from autosave
  Future<Project> restoreAutoSaveProject() async {
    final projectsDirectory = await _getProjectsDirectory();
    final autosaveDirectory = Directory(
      path.join(projectsDirectory.path, 'autosave'),
    );

    if (!autosaveDirectory.existsSync()) {
      throw Exception('No autosave project found');
    }

    // In a real app, we would load the project from the autosave directory
    // For this prototype, we'll return a dummy project
    return Project(
      id: 'autosave',
      name: 'Auto-saved Project',
      path: autosaveDirectory.path,
      createdAt: autosaveDirectory.statSync().modified,
      lastModified: DateTime.now(),
      clips: [],
      audioTracks: [],
      textOverlays: [],
      transitions: [],
      editHistory: [],
    );
  }

  // Backup a project to cloud
  Future<void> backupProjectToCloud(Project project) async {
    // In a real app, this would use a cloud storage service
    // For this prototype, we'll just simulate the operation

    // Simulate processing time
    await Future.delayed(Duration(seconds: 2));
  }

  // Restore a project from cloud
  Future<Project> restoreProjectFromCloud(String backupId) async {
    // In a real app, this would use a cloud storage service
    // For this prototype, we'll just simulate the operation

    // Simulate processing time
    await Future.delayed(Duration(seconds: 2));

    return Project(
      id: backupId,
      name: 'Cloud Project $backupId',
      path: (await _getProjectsDirectory()).path,
      createdAt: DateTime.now().subtract(Duration(days: 7)),
      lastModified: DateTime.now().subtract(Duration(hours: 1)),
      clips: [],
      audioTracks: [],
      textOverlays: [],
      transitions: [],
      editHistory: [],
    );
  }

  // Export a project (without final video)
  Future<String> exportProject(Project project) async {
    final exportDirectory = await _getTempDirectory();
    final exportPath = path.join(
      exportDirectory.path,
      '${project.name}_project_export',
    );
    final exportProjectDirectory = Directory(exportPath);

    if (exportProjectDirectory.existsSync()) {
      exportProjectDirectory.deleteSync(recursive: true);
    }

    exportProjectDirectory.createSync();

    // In a real app, we would copy all project files to the export directory
    // For this prototype, we'll just simulate the operation

    // Save project metadata
    // File('$exportPath/project.json').writeAsString(jsonEncode(project));

    return exportPath;
  }

  // Import a project
  Future<Project> importProject(String projectPath) async {
    // In a real app, this would parse a project file and load its contents
    // For this prototype, we'll simulate loading a project

    // Simulate processing time
    await Future.delayed(Duration(seconds: 2));

    final directory = await _getProjectsDirectory();
    final importedPath = path.join(
      directory.path,
      'imported_${DateTime.now().millisecondsSinceEpoch}',
    );

    // In a real app, we would copy all project files to the projects directory
    // For this prototype, we'll just simulate the operation

    return Project(
      id: 'imported_${DateTime.now().millisecondsSinceEpoch}',
      name: 'Imported Project',
      path: importedPath,
      createdAt: DateTime.now(),
      lastModified: DateTime.now(),
      clips: [],
      audioTracks: [],
      textOverlays: [],
      transitions: [],
      editHistory: [],
    );
  }

  // Save a project snapshot for undo/redo
  Future<void> saveUndoSnapshot(Project project) async {
    // In a real app, this would save a snapshot of the project
    // For this prototype, we'll just simulate the operation

    // Simulate processing time
    await Future.delayed(Duration(milliseconds: 500));
  }

  // Autosave the project periodically
  Future<void> startAutoSave(Project project) async {
    // In a real app, this would save the project periodically
    // For this prototype, we'll just simulate the operation

    // Simulate autosave every 5 minutes
    await Future.delayed(Duration(minutes: 5));

    // Save project
    await saveProjectSnapshot(project, autoSave: true);

    // Show notification (would be handled by UI layer in a real app)
    // In production, this would use a proper logging framework
    // debugPrint('Project auto-saved: ${project.name}');

    // Continue autosaving
    startAutoSave(project);
  }

  // Clear autosave files
  Future<void> clearAutoSaveFiles() async {
    final projectsDirectory = await _getProjectsDirectory();
    final autosaveDirectory = Directory(
      path.join(projectsDirectory.path, 'autosave'),
    );

    if (autosaveDirectory.existsSync()) {
      autosaveDirectory.deleteSync(recursive: true);
    }

    // Recreate the autosave directory
    autosaveDirectory.createSync();
  }
}
