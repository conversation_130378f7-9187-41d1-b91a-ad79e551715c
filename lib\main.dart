import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';
import 'package:provider/provider.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:image_picker/image_picker.dart';

void main() {
  runApp(const VideoEditorApp());
}

class VideoEditorApp extends StatelessWidget {
  const VideoEditorApp({super.key});

  @override
  Widget build(BuildContext context) {
    return const MaterialApp(
      title: 'MontagePro',
      home: VideoEditorHome(),
      debugShowCheckedModeBanner: false,
    );
  }
}

class VideoEditorHome extends StatefulWidget {
  const VideoEditorHome({super.key});

  @override
  State<VideoEditorHome> createState() => _VideoEditorHomeState();
}

class _VideoEditorHomeState extends State<VideoEditorHome> {
  late VideoPlayerController _controller;
  final ImagePicker _picker = ImagePicker();
  List<XFile> _mediaFiles = [];
  // Editing tools state
  bool _showEditingTools = false;
  double _selectedStart = 0;
  double _selectedEnd = 5;
  double _rotation = 0.0;
  double _brightness = 1.0;
  double _contrast = 1.0;
  double _saturation = 1.0;
  double _zoom = 1.0;
  // Undo/Redo functionality
  List<EditAction> _editHistory = [];
  List<EditAction> _redoStack = [];
  // Effects and filters state
  bool _showEffectsPanel = false;
  String _selectedFilter = 'None';
  bool _enableGrain = false;
  bool _enableVignette = false;
  bool _enableLomo = false;
  bool _enableSepia = false;
  // Audio management state
  bool _showAudioPanel = false;
  double _musicVolume = 1.0;
  double _voiceVolume = 0.5;
  bool _enableFadeIn = false;
  bool _enableFadeOut = false;
  bool _enableDucking = false;
  List<AudioTrack> _audioTracks = [];
  // Text overlay state
  bool _showTextPanel = false;
  List<TextOverlay> _textOverlays = [];
  String _currentText = '';
  double _textSize = 24.0;
  Color _textColor = Colors.white;
  TextAlign _textAlign = TextAlign.center;
  FontStyle _textStyle = FontStyle.normal;
  FontWeight _textWeight = FontWeight.normal;
  // Transitions library state
  bool _showTransitionsPanel = false;
  List<Transition> _transitionLibrary = [];
  TransitionType _selectedTransitionType = TransitionType.fade;
  double _transitionDuration = 1.0;
  // Export and sharing state
  bool _showExportPanel = false;
  ExportFormat _selectedFormat = ExportFormat.mp4;
  ExportResolution _selectedResolution = ExportResolution.p1080;
  bool _enableHardwareAcceleration = true;
  bool _optimizeForStreaming = true;
  String _exportProgress = '';
  // PiP (Picture in Picture) functionality
  bool _enablePiP = false;
  Rect _pipPosition = const Rect.fromLTWH(20, 20, 120, 90);
  double _pipOpacity = 1.0;
  double _pipCornerRadius = 8.0;
  // PiP functionality state
  bool _showPiPControls = false;
  // Story format functionality
  bool _enableStoryFormat = false;
  StoryFormat _selectedStoryFormat = StoryFormat.instagram;
  bool _showStoryFormatPanel = false;

  void showEditingTools() {
    setState(() {
      _showEditingTools = true;
    });
  }

  Future<void> _importMedia({
    required ImageSource source,
    required String mediaType,
  }) async {
    if (mediaType == 'video') {
      final XFile? pickedFile = await _picker.pickVideo(source: source);
      if (pickedFile != null) {
        setState(() {
          _mediaFiles.add(pickedFile);
          // If this is the first media file, update the timeline
          if (_mediaFiles.length == 1) {
            _controller = VideoPlayerController.file(File(pickedFile.path))
              ..initialize().then((_) {
                setState(() {});
              });
          }
        });
      }
    }
  }

  @override
  void initState() {
    super.initState();
    // Initialize with one clip
    _clips.add(Clip(start: 0, end: 5, color: widget.color));

    // Initialize transition library
    _transitionLibrary.addAll(
      TransitionType.values.map((type) => Transition(type: type)).toList(),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('MontagePro'),
        actions: [
          IconButton(
            icon: const Icon(FontAwesomeIcons.rotateBackward),
            onPressed: () {
              _undo();
            },
            tooltip: 'Undo',
          ),
          IconButton(
            icon: const Icon(FontAwesomeIcons.rotateForward),
            onPressed: () {
              _redo();
            },
            tooltip: 'Redo',
          ),
          const SizedBox(width: 16),
          IconButton(
            icon: const Icon(FontAwesomeIcons.save),
            onPressed: () {
              // Save project
            },
          ),
          IconButton(
            icon: const Icon(FontAwesomeIcons.shareSquare),
            onPressed: () {
              _showExportPanel();
            },
          ),
          IconButton(
            icon: const Icon(Icons.volume_up),
            onPressed: () {
              showAudioPanel();
            },
          ),
          IconButton(
            icon: const Icon(Icons.text_fields),
            onPressed: () {
              setState(() {
                _showTextPanel = true;
              });
            },
          ),
          IconButton(
            icon: const Icon(Icons.swap_horiz),
            onPressed: () {
              setState(() {
                _showTransitionsPanel = true;
              });
            },
          ),
          IconButton(
            icon: const Icon(Icons.video_call),
            onPressed: () {
              _showPiPControls();
            },
          ),
          IconButton(
            icon: const Icon(Icons.photo_size_select_actual),
            onPressed: () {
              _showStoryFormatPanel();
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Video preview at the top
          Expanded(
            flex: 3,
            child: Container(
              color: Colors.black,
              child:
                  _controller.value.isInitialized
                      ? Column(
                        children: [
                          AspectRatio(
                            aspectRatio: _controller.value.aspectRatio,
                            child: VideoPlayer(_controller),
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              IconButton(
                                icon: Icon(
                                  _controller.value.isPlaying
                                      ? Icons.pause
                                      : Icons.play_arrow,
                                ),
                                onPressed: () {
                                  setState(() {
                                    _controller.value.isPlaying
                                        ? _controller.pause()
                                        : _controller.play();
                                  });
                                },
                              ),
                              IconButton(
                                icon: const Icon(Icons.edit),
                                onPressed: () {
                                  setState(() {
                                    _showEffectsPanel = true;
                                  });
                                },
                              ),
                            ],
                          ),
                        ],
                      )
                      : const Center(child: Text('Preview Area')),
            ),
          ),
          // Timeline at the bottom
          Expanded(
            flex: 2,
            child: Container(
              color: Colors.grey[800],
              child: Column(
                children: [
                  // Timeline controls
                  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text('Timeline', style: TextStyle(color: Colors.white)),
                        Row(
                          children: [
                            IconButton(
                              icon: Icon(Icons.zoom_in, color: Colors.white),
                              onPressed: () {
                                // Zoom in
                              },
                            ),
                            IconButton(
                              icon: Icon(Icons.zoom_out, color: Colors.white),
                              onPressed: () {
                                // Zoom out
                              },
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  // Video track
                  Track(
                    type: 'video',
                    color: Colors.blue[300],
                    duration: _controller.value.duration.inSeconds.toDouble(),
                    enableTransitions: true,
                  ),
                  // Audio track
                  Track(
                    type: 'audio',
                    color: Colors.green[300],
                    duration: _controller.value.duration.inSeconds.toDouble(),
                  ),
                  // Text track
                  Track(
                    type: 'text',
                    color: Colors.orange[300],
                    duration: _controller.value.duration.inSeconds.toDouble(),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showMediaOptions,
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildEditingTools() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.grey[900],
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Editing Tools',
                style: TextStyle(color: Colors.white, fontSize: 16),
              ),
              IconButton(
                icon: Icon(Icons.close, color: Colors.white),
                onPressed: () {
                  setState(() {
                    _showEditingTools = false;
                  });
                },
              ),
            ],
          ),
          // Basic editing tools
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildEditingButton(Icons.content_cut, 'Cut', _cutSelection),
              _buildEditingButton(Icons.crop, 'Crop', _applyCrop),
              _buildEditingButton(Icons.rotate_right, 'Rotate', _rotateVideo),
              _buildEditingButton(Icons.flip, 'Flip', _flipVideo),
            ],
          ),
          const SizedBox(height: 16),
          // Adjustment tools
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildSliderTool('Brightness', _brightness, (value) {
                setState(() {
                  _brightness = value;
                  _applyAdjustments();
                });
              }),
              _buildSliderTool('Contrast', _contrast, (value) {
                setState(() {
                  _contrast = value;
                  _applyAdjustments();
                });
              }),
              _buildSliderTool('Saturation', _saturation, (value) {
                setState(() {
                  _saturation = value;
                  _applyAdjustments();
                });
              }),
              _buildSliderTool('Zoom', _zoom, (value) {
                setState(() {
                  _zoom = value;
                  _applyZoom();
                });
              }),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildEditingButton(
    IconData icon,
    String label,
    VoidCallback onPressed,
  ) {
    return Column(
      children: [
        IconButton(icon: Icon(icon, color: Colors.white), onPressed: onPressed),
        Text(label, style: const TextStyle(color: Colors.white, fontSize: 12)),
      ],
    );
  }

  Widget _buildSliderTool(
    String label,
    double value,
    Function(double) onChanged,
  ) {
    return Column(
      children: [
        Text(label, style: const TextStyle(color: Colors.white, fontSize: 12)),
        Slider(
          value: value,
          min: 0.0,
          max: 2.0,
          onChanged: onChanged,
          activeColor: Colors.orange,
        ),
        Text(
          '${value.toStringAsFixed(1)}',
          style: const TextStyle(color: Colors.white, fontSize: 12),
        ),
      ],
    );
  }

  void _cutSelection() {
    // Logic to cut the selected portion of the video
    setState(() {
      _controller.pause();
      // Split the current clip into two if needed
      // This would typically involve creating a new video file
    });
  }

  void _applyCrop() {
    // Logic to apply cropping to the video
    // This would typically involve creating a new video file with cropped dimensions
  }

  void _rotateVideo() {
    setState(() {
      _rotation = (_rotation + 90) % 360;
      // Apply rotation to the video
    });
  }

  void _flipVideo() {
    // Logic to flip the video horizontally or vertically
  }

  void _applyAdjustments() {
    // Logic to apply brightness, contrast, and saturation adjustments
  }

  void _applyZoom() {
    // Logic to apply zoom to the video
  }

  void _showMediaOptions() {
    showModalBottomSheet(
      context: context,
      builder: (context) {
        return Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('Add Media', style: Theme.of(context).textTheme.headline6),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  Column(
                    children: [
                      IconButton(
                        icon: const Icon(Icons.video_library),
                        onPressed: () {
                          Navigator.pop(context);
                          _importMedia(
                            source: ImageSource.gallery,
                            mediaType: 'video',
                          );
                        },
                        iconSize: 48,
                      ),
                      const Text('Video'),
                    ],
                  ),
                  Column(
                    children: [
                      IconButton(
                        icon: const Icon(Icons.photo_library),
                        onPressed: () {
                          Navigator.pop(context);
                          _importPhoto();
                        },
                        iconSize: 48,
                      ),
                      const Text('Photo'),
                    ],
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Future<void> _importPhoto() async {
    final XFile? pickedFile = await _picker.pickImage(
      source: ImageSource.gallery,
    );
    if (pickedFile != null) {
      setState(() {
        _mediaFiles.add(pickedFile);
        // If this is the first media file, update the timeline
        if (_mediaFiles.length == 1) {
          _controller = VideoPlayerController.file(File(pickedFile.path))
            ..initialize().then((_) {
              setState(() {});
            });
        }
      });
    }
  }

  Widget _buildEffectsPanel() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.grey[900],
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Effects & Filters',
                style: TextStyle(color: Colors.white, fontSize: 16),
              ),
              IconButton(
                icon: Icon(Icons.close, color: Colors.white),
                onPressed: () {
                  setState(() {
                    _showEffectsPanel = false;
                  });
                },
              ),
            ],
          ),
          // Filter selection
          Row(
            children: [
              Text('Filter:', style: TextStyle(color: Colors.white)),
              const SizedBox(width: 16),
              Expanded(
                child: DropdownButton<String>(
                  isExpanded: true,
                  value: _selectedFilter,
                  items: const [
                    DropdownMenuItem(value: 'None', child: Text('None')),
                    DropdownMenuItem(value: 'Vintage', child: Text('Vintage')),
                    DropdownMenuItem(value: 'Cinema', child: Text('Cinema')),
                    DropdownMenuItem(
                      value: 'Black & White',
                      child: Text('Black & White'),
                    ),
                    DropdownMenuItem(
                      value: 'Nostalgia',
                      child: Text('Nostalgia'),
                    ),
                  ],
                  onChanged: (String? newValue) {
                    if (newValue != null) {
                      setState(() {
                        _selectedFilter = newValue;
                        _applyFilter();
                      });
                    }
                  },
                  style: TextStyle(color: Colors.white),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          // Creative effects
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildToggleEffect('Grain', _enableGrain, (value) {
                setState(() {
                  _enableGrain = value!;
                  _applyEffects();
                });
              }),
              _buildToggleEffect('Vignette', _enableVignette, (value) {
                setState(() {
                  _enableVignette = value!;
                  _applyEffects();
                });
              }),
              _buildToggleEffect('Lomo', _enableLomo, (value) {
                setState(() {
                  _enableLomo = value!;
                  _applyEffects();
                });
              }),
              _buildToggleEffect('Sepia', _enableSepia, (value) {
                setState(() {
                  _enableSepia = value!;
                  _applyEffects();
                });
              }),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildToggleEffect(
    String label,
    bool value,
    Function(bool?) onChanged,
  ) {
    return Column(
      children: [
        Transform.scale(
          scale: 0.8,
          child: Switch(
            value: value,
            onChanged: onChanged,
            activeColor: Colors.orange,
          ),
        ),
        Text(label, style: const TextStyle(color: Colors.white, fontSize: 12)),
      ],
    );
  }

  void _applyFilter() {
    // Logic to apply the selected filter to the video
    // This would typically involve using a video processing library
  }

  void _applyEffects() {
    // Logic to apply creative effects to the video
    // This would typically involve using a video processing library
  }

  void showAudioPanel() {
    setState(() {
      _showAudioPanel = true;
    });
  }

  Widget _buildAudioPanel() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.grey[900],
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Audio Management',
                style: TextStyle(color: Colors.white, fontSize: 16),
              ),
              IconButton(
                icon: Icon(Icons.close, color: Colors.white),
                onPressed: () {
                  setState(() {
                    _showAudioPanel = false;
                  });
                },
              ),
            ],
          ),
          // Volume controls
          Row(
            children: [
              Text('Music Volume', style: TextStyle(color: Colors.white)),
              const SizedBox(width: 16),
              Expanded(
                child: Slider(
                  value: _musicVolume,
                  min: 0.0,
                  max: 1.0,
                  onChanged: (value) {
                    setState(() {
                      _musicVolume = value;
                      _applyAudioSettings();
                    });
                  },
                  activeColor: Colors.orange,
                ),
              ),
              Text(
                '${(_musicVolume * 100).toInt()}%',
                style: TextStyle(color: Colors.white),
              ),
            ],
          ),
          Row(
            children: [
              Text('Voice Volume', style: TextStyle(color: Colors.white)),
              const SizedBox(width: 16),
              Expanded(
                child: Slider(
                  value: _voiceVolume,
                  min: 0.0,
                  max: 1.0,
                  onChanged: (value) {
                    setState(() {
                      _voiceVolume = value;
                      _applyAudioSettings();
                    });
                  },
                  activeColor: Colors.orange,
                ),
              ),
              Text(
                '${(_voiceVolume * 100).toInt()}%',
                style: TextStyle(color: Colors.white),
              ),
            ],
          ),
          // Audio effects
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildToggleEffect('Fade In', _enableFadeIn, (value) {
                setState(() {
                  _enableFadeIn = value!;
                  _applyAudioSettings();
                });
              }),
              _buildToggleEffect('Fade Out', _enableFadeOut, (value) {
                setState(() {
                  _enableFadeOut = value!;
                  _applyAudioSettings();
                });
              }),
              _buildToggleEffect('Ducking', _enableDucking, (value) {
                setState(() {
                  _enableDucking = value!;
                  _applyAudioSettings();
                });
              }),
            ],
          ),
          // Audio tracks
          const SizedBox(height: 16),
          Text(
            'Audio Tracks',
            style: TextStyle(color: Colors.white, fontSize: 14),
          ),
          const SizedBox(height: 8),
          Expanded(
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: _audioTracks.length,
              itemBuilder: (context, index) {
                return _buildAudioTrackItem(_audioTracks[index]);
              },
            ),
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildEditingButton(Icons.add, 'Add Track', _addAudioTrack),
              const SizedBox(width: 16),
              _buildEditingButton(
                Icons.audiotrack,
                'Import Music',
                _importAudio,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAudioTrackItem(AudioTrack track) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4),
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.grey[800],
        borderRadius: BorderRadius.circular(4),
      ),
      child: Row(
        children: [
          Icon(Icons.audiotrack, color: track.color),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              track.name,
              style: TextStyle(color: Colors.white),
              overflow: TextOverflow.ellipsis,
            ),
          ),
          IconButton(
            icon: Icon(Icons.edit, color: Colors.white),
            onPressed: () {
              _showAudioTrackEditor(track);
            },
            iconSize: 16,
          ),
          IconButton(
            icon: Icon(Icons.delete, color: Colors.white),
            onPressed: () {
              _removeAudioTrack(track);
            },
            iconSize: 16,
          ),
        ],
      ),
    );
  }

  void _addAudioTrack() {
    setState(() {
      _audioTracks.add(
        AudioTrack(
          id: DateTime.now().millisecondsSinceEpoch,
          name: 'Audio Track ${_audioTracks.length + 1}',
          volume: 1.0,
          mute: false,
          solo: false,
          color:
              Colors.primaries[_audioTracks.length % Colors.primaries.length],
        ),
      );
    });
  }

  void _removeAudioTrack(AudioTrack track) {
    setState(() {
      _audioTracks.remove(track);
    });
  }

  void _showAudioTrackEditor(AudioTrack track) {
    // Show dialog to edit track properties
    // This would typically involve a modal dialog with track settings
  }

  void _importAudio() {
    // Logic to import audio from device or record voice
  }

  void _applyAudioSettings() {
    // Logic to apply audio settings to the video
  }

  Widget _buildTextPanel() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.grey[900],
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Text Overlay',
                style: TextStyle(color: Colors.white, fontSize: 16),
              ),
              IconButton(
                icon: Icon(Icons.close, color: Colors.white),
                onPressed: () {
                  setState(() {
                    _showTextPanel = false;
                  });
                },
              ),
            ],
          ),
          // Text input
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: TextEditingController(text: _currentText),
                  onChanged: (value) {
                    setState(() {
                      _currentText = value;
                    });
                  },
                  style: TextStyle(color: Colors.white, fontSize: _textSize),
                  maxLines: null,
                  decoration: InputDecoration(
                    hintText: 'Enter text',
                    hintStyle: TextStyle(color: Colors.grey[600]),
                    border: InputBorder.none,
                    filled: true,
                    fillColor: Colors.grey[800],
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          // Text styling
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildTextStylingButton('Size', Icons.text_fields, () {
                _showTextSizeSelector();
              }),
              _buildTextStylingButton('Color', Icons.color_lens, () {
                _showTextColorPicker();
              }),
              _buildTextStylingButton('Align', Icons.format_align_center, () {
                _showTextAlignmentSelector();
              }),
              _buildTextStylingButton('Style', Icons.text_format, () {
                _showTextStyleSelector();
              }),
            ],
          ),
          const SizedBox(height: 16),
          // Text preview
          Container(
            margin: const EdgeInsets.symmetric(vertical: 16),
            height: 100,
            decoration: BoxDecoration(
              color: Colors.black,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Center(
              Text(
                _currentText.isNotEmpty ? _currentText : 'Text Preview',
                style: TextStyle(
                  color: _textColor,
                  fontSize: _textSize,
                  fontStyle: _textStyle,
                  fontWeight: _textWeight,
                ),
                textAlign: _textAlign,
              ),
            ),
          ),
          const SizedBox(height: 16),
          // Add text button
          ElevatedButton.icon(
            onPressed: _currentText.isNotEmpty ? _addTextOverlay : null,
            icon: Icon(Icons.add),
            label: Text('Add Text to Video'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              foregroundColor: Colors.black,
            ),
          ),
          const SizedBox(height: 16),
          // Existing text overlays
          if (_textOverlays.isNotEmpty)
            Column(
              children: [
                Text(
                  'Existing Texts',
                  style: TextStyle(color: Colors.white, fontSize: 14),
                ),
                const SizedBox(height: 8),
                Expanded(
                  child: ListView.builder(
                    shrinkWrap: true,
                    itemCount: _textOverlays.length,
                    itemBuilder: (context, index) {
                      return _buildTextOverlayItem(_textOverlays[index]);
                    },
                  ),
                ),
              ],
            ),
        ],
      ),
    );
  }

  Widget _buildTextStylingButton(
    String label,
    IconData icon,
    VoidCallback onPressed,
  ) {
    return Column(
      children: [
        IconButton(icon: Icon(icon, color: Colors.white), onPressed: onPressed),
        Text(label, style: const TextStyle(color: Colors.white, fontSize: 12)),
      ],
    );
  }

  Widget _buildTextOverlayItem(TextOverlay overlay) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4),
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.grey[800],
        borderRadius: BorderRadius.circular(4),
      ),
      child: Row(
        children: [
          Expanded(
            child: Text(
              overlay.text,
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                color: overlay.color,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
          IconButton(
            icon: Icon(Icons.edit, color: Colors.white),
            onPressed: () {
              setState(() {
                _currentText = overlay.text;
                _textSize = overlay.fontSize;
                _textColor = overlay.color;
                _textAlign = overlay.align;
                _textStyle = overlay.style;
                _textWeight = overlay.weight;
              });
              _textOverlays.removeAt(overlay.index);
              setState(() {});
            },
            iconSize: 16,
          ),
          IconButton(
            icon: Icon(Icons.delete, color: Colors.white),
            onPressed: () {
              _removeTextOverlay(overlay);
            },
            iconSize: 16,
          ),
        ],
      ),
    );
  }

  void _addTextOverlay() {
    setState(() {
      _textOverlays.add(
        TextOverlay(
          index: _textOverlays.length,
          text: _currentText,
          fontSize: _textSize,
          color: _textColor,
          align: _textAlign,
          style: _textStyle,
          weight: _textWeight,
        ),
      );
      _currentText = '';
    });
  }

  void _removeTextOverlay(TextOverlay overlay) {
    setState(() {
      _textOverlays.removeAt(overlay.index);
    });
  }

  void _showTextSizeSelector() {
    showModalBottomSheet(
      context: context,
      builder: (context) {
        return Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Select Text Size',
                style: Theme.of(context).textTheme.headline6,
              ),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  _buildTextSizeButton(16.0),
                  _buildTextSizeButton(24.0),
                  _buildTextSizeButton(32.0),
                  _buildTextSizeButton(40.0),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildTextSizeButton(double size) {
    return TextButton(
      onPressed: () {
        setState(() {
          _textSize = size;
        });
        Navigator.pop(context);
      },
      child: Text(
        '$size',
        style: TextStyle(fontSize: size, color: Colors.black),
      ),
    );
  }

  void _showTextColorPicker() {
    showModalBottomSheet(
      context: context,
      builder: (context) {
        return Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Select Text Color',
                style: Theme.of(context).textTheme.headline6,
              ),
              const SizedBox(height: 16),
              SizedBox(
                height: 300,
                child: ColorPicker(
                  pickerColor: _textColor,
                  onColorChanged: (color) {
                    setState(() {
                      _textColor = color;
                    });
                  },
                  enableAlpha: false,
                  pickerAreaHeightPercent: 0.8,
                ),
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: Navigator.of(context).pop,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange,
                  foregroundColor: Colors.black,
                ),
                child: const Text('Done'),
              ),
            ],
          ),
        );
      },
    );
  }

  void _showTextAlignmentSelector() {
    showModalBottomSheet(
      context: context,
      builder: (context) {
        return Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Select Text Alignment',
                style: Theme.of(context).textTheme.headline6,
              ),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  _buildTextAlignmentButton(
                    'Left',
                    Icons.format_align_left,
                    TextAlign.left,
                  ),
                  _buildTextAlignmentButton(
                    'Center',
                    Icons.format_align_center,
                    TextAlign.center,
                  ),
                  _buildTextAlignmentButton(
                    'Right',
                    Icons.format_align_right,
                    TextAlign.right,
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildTextAlignmentButton(
    String label,
    IconData icon,
    TextAlign alignment,
  ) {
    return TextButton(
      onPressed: () {
        setState(() {
          _textAlign = alignment;
        });
        Navigator.pop(context);
      },
      child: Column(children: [Icon(icon, color: Colors.black), Text(label)]),
    );
  }

  void _showTextStyleSelector() {
    showModalBottomSheet(
      context: context,
      builder: (context) {
        return Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Select Text Style',
                style: Theme.of(context).textTheme.headline6,
              ),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  _buildTextStyleButton(
                    'Normal',
                    Icons.text_fields,
                    FontStyle.normal,
                    FontWeight.normal,
                  ),
                  _buildTextStyleButton(
                    'Italic',
                    Icons.format_italic,
                    FontStyle.italic,
                    FontWeight.normal,
                  ),
                  _buildTextStyleButton(
                    'Bold',
                    Icons.format_bold,
                    FontStyle.normal,
                    FontWeight.bold,
                  ),
                  _buildTextStyleButton(
                    'Bold Italic',
                    Icons.format_bold,
                    FontStyle.italic,
                    FontWeight.bold,
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildTextStyleButton(
    String label,
    IconData icon,
    FontStyle style,
    FontWeight weight,
  ) {
    return TextButton(
      onPressed: () {
        setState(() {
          _textStyle = style;
          _textWeight = weight;
        });
        Navigator.pop(context);
      },
      child: Column(
        children: [
          Icon(icon, color: Colors.black),
          Text(label, style: TextStyle(fontWeight: weight, fontStyle: style)),
        ],
      ),
    );
  }

  Widget _buildTransitionsPanel() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.grey[900],
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Transitions Library',
                style: TextStyle(color: Colors.white, fontSize: 16),
              ),
              IconButton(
                icon: Icon(Icons.close, color: Colors.white),
                onPressed: () {
                  setState(() {
                    _showTransitionsPanel = false;
                  });
                },
              ),
            ],
          ),
          // Transition type selection
          Row(
            children: [
              Text('Transition Type:', style: TextStyle(color: Colors.white)),
              const SizedBox(width: 16),
              Expanded(
                child: DropdownButton<TransitionType>(
                  isExpanded: true,
                  value: _selectedTransitionType,
                  items:
                      TransitionType.values.map((type) {
                        return DropdownMenuItem(
                          value: type,
                          child: Text(_getTransitionName(type)),
                        );
                      }).toList(),
                  onChanged: (TransitionType? newValue) {
                    if (newValue != null) {
                      setState(() {
                        _selectedTransitionType = newValue;
                      });
                    }
                  },
                  style: TextStyle(color: Colors.white),
                ),
              ),
            ],
          ),
          // Transition duration
          Row(
            children: [
              Text(
                'Duration: ${_transitionDuration.toInt()}s',
                style: TextStyle(color: Colors.white),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Slider(
                  value: _transitionDuration,
                  min: 0.5,
                  max: 5.0,
                  divisions: 9,
                  label: '${_transitionDuration.toInt()}s',
                  onChanged: (value) {
                    setState(() {
                      _transitionDuration = value;
                    });
                  },
                  activeColor: Colors.orange,
                ),
              ),
            ],
          ),
          // Preview
          Container(
            margin: const EdgeInsets.symmetric(vertical: 16),
            height: 100,
            decoration: BoxDecoration(
              color: Colors.black,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Center(child: _buildTransitionPreview()),
          ),
          // Apply transition button
          ElevatedButton.icon(
            onPressed: _applySelectedTransition,
            icon: Icon(Icons.add),
            label: Text('Apply Transition'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              foregroundColor: Colors.black,
            ),
          ),
          const SizedBox(height: 16),
          // Transition library
          Text(
            'Transition Library',
            style: TextStyle(color: Colors.white, fontSize: 14),
          ),
          const SizedBox(height: 8),
          Expanded(
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: _transitionLibrary.length,
              itemBuilder: (context, index) {
                return _buildTransitionItem(_transitionLibrary[index]);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTransitionPreview() {
    // This would be a visual preview of the selected transition
    // For now, we'll use a simple icon based on the transition type
    return Icon(
      _getTransitionIcon(_selectedTransitionType),
      color: Colors.white,
      size: 48,
    );
  }

  Widget _buildTransitionItem(Transition transition) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4),
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color:
            transition.type == _selectedTransitionType
                ? Colors.orange.withOpacity(0.3)
                : Colors.grey[800],
        borderRadius: BorderRadius.circular(4),
      ),
      child: Row(
        children: [
          Icon(_getTransitionIcon(transition.type), color: Colors.white),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              _getTransitionName(transition.type),
              style: TextStyle(color: Colors.white),
            ),
          ),
          if (transition.type == _selectedTransitionType)
            Icon(Icons.check, color: Colors.orange),
        ],
      ),
    );
  }

  void _applySelectedTransition() {
    // Logic to apply the selected transition between clips
    // This would typically involve using a video processing library
  }

  IconData _getTransitionIcon(TransitionType type) {
    switch (type) {
      case TransitionType.fade:
        return Icons.opacity;
      case TransitionType.slide:
        return Icons.arrow_right_alt;
      case TransitionType.zoom:
        return Icons.zoom_out;
      case TransitionType.rotate:
        return Icons.rotate_right;
      case TransitionType.flip:
        return Icons.flip;
      case TransitionType.crossFade:
        return Icons.compare_arrows;
      case TransitionType.dissolve:
        return Icons.blur_on;
      case TransitionType.wipe:
        return Icons.arrow_right_alt;
      case TransitionType.push:
        return Icons.arrow_forward;
      case TransitionType.cube:
        return Icons.layers;
      case TransitionType.door:
        return Icons.door_back_door;
    }
  }

  String _getTransitionName(TransitionType type) {
    switch (type) {
      case TransitionType.fade:
        return 'Fade';
      case TransitionType.slide:
        return 'Slide';
      case TransitionType.zoom:
        return 'Zoom';
      case TransitionType.rotate:
        return 'Rotate';
      case TransitionType.flip:
        return 'Flip';
      case TransitionType.crossFade:
        return 'Cross Fade';
      case TransitionType.dissolve:
        return 'Dissolve';
      case TransitionType.wipe:
        return 'Wipe';
      case TransitionType.push:
        return 'Push';
      case TransitionType.cube:
        return 'Cube';
      case TransitionType.door:
        return 'Door';
    }
  }

  void _showExportPanel() {
    setState(() {
      _showExportPanel = true;
    });
  }

  Widget _buildExportPanel() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.grey[900],
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Export & Share',
                style: TextStyle(color: Colors.white, fontSize: 16),
              ),
              IconButton(
                icon: Icon(Icons.close, color: Colors.white),
                onPressed: () {
                  setState(() {
                    _showExportPanel = false;
                  });
                },
              ),
            ],
          ),
          // Export settings
          Row(
            children: [
              Text('Format:', style: TextStyle(color: Colors.white)),
              const SizedBox(width: 16),
              Expanded(
                child: DropdownButton<ExportFormat>(
                  isExpanded: true,
                  value: _selectedFormat,
                  items:
                      ExportFormat.values.map((format) {
                        return DropdownMenuItem(
                          value: format,
                          child: Text(_getExportFormatName(format)),
                        );
                      }).toList(),
                  onChanged: (ExportFormat? newValue) {
                    if (newValue != null) {
                      setState(() {
                        _selectedFormat = newValue;
                      });
                    }
                  },
                  style: TextStyle(color: Colors.white),
                ),
              ),
            ],
          ),
          Row(
            children: [
              Text('Resolution:', style: TextStyle(color: Colors.white)),
              const SizedBox(width: 16),
              Expanded(
                child: DropdownButton<ExportResolution>(
                  isExpanded: true,
                  value: _selectedResolution,
                  items:
                      ExportResolution.values.map((resolution) {
                        return DropdownMenuItem(
                          value: resolution,
                          child: Text(_getExportResolutionName(resolution)),
                        );
                      }).toList(),
                  onChanged: (ExportResolution? newValue) {
                    if (newValue != null) {
                      setState(() {
                        _selectedResolution = newValue;
                      });
                    }
                  },
                  style: TextStyle(color: Colors.white),
                ),
              ),
            ],
          ),
          Row(
            children: [
              Text(
                'Hardware Acceleration',
                style: TextStyle(color: Colors.white),
              ),
              const SizedBox(width: 16),
              Switch(
                value: _enableHardwareAcceleration,
                onChanged: (value) {
                  setState(() {
                    _enableHardwareAcceleration = value;
                  });
                },
                activeColor: Colors.orange,
              ),
            ],
          ),
          Row(
            children: [
              Text(
                'Optimize for Streaming',
                style: TextStyle(color: Colors.white),
              ),
              const SizedBox(width: 16),
              Switch(
                value: _optimizeForStreaming,
                onChanged: (value) {
                  setState(() {
                    _optimizeForStreaming = value;
                  });
                },
                activeColor: Colors.orange,
              ),
            ],
          ),
          const SizedBox(height: 16),
          // Export progress
          if (_exportProgress.isNotEmpty)
            Column(
              children: [
                LinearProgressIndicator(
                  value:
                      _exportProgress.contains('%')
                          ? double.parse(_exportProgress.replaceAll('%', '')) /
                              100
                          : 0,
                  color: Colors.orange,
                  backgroundColor: Colors.grey[700],
                ),
                const SizedBox(height: 8),
                Text(
                  'Export Progress: $_exportProgress',
                  style: TextStyle(color: Colors.white),
                ),
              ],
            ),
          // Export button
          ElevatedButton.icon(
            onPressed: _exportVideo,
            icon: Icon(Icons.file_download),
            label: Text('Export Video'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              foregroundColor: Colors.black,
              minimumSize: const Size(double.infinity, 50),
            ),
          ),
          const SizedBox(height: 16),
          // Share options
          Text(
            'Quick Share',
            style: TextStyle(color: Colors.white, fontSize: 14),
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildShareButton('WhatsApp', Icons.chat, Colors.green),
              _buildShareButton('Instagram', Icons.camera_alt, Colors.pink),
              _buildShareButton('TikTok', Icons.music_note, Colors.black),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildShareButton(String platform, IconData icon, Color color) {
    return Expanded(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 4.0),
        child: ElevatedButton.icon(
          onPressed: () => _shareVideo(platform),
          icon: Icon(icon, color: Colors.white),
          label: Text(platform),
          style: ElevatedButton.styleFrom(
            backgroundColor: color,
            foregroundColor: Colors.white,
          ),
        ),
      ),
    );
  }

  String _getExportFormatName(ExportFormat format) {
    switch (format) {
      case ExportFormat.mp4:
        return 'MP4 (H.264)';
      case ExportFormat.mov:
        return 'MOV (QuickTime)';
      case ExportFormat.webm:
        return 'WEBM';
      case ExportFormat.gif:
        return 'GIF';
    }
  }

  String _getExportResolutionName(ExportResolution resolution) {
    switch (resolution) {
      case ExportResolution.p480:
        return '480p';
      case ExportResolution.p720:
        return '720p';
      case ExportResolution.p1080:
        return '1080p';
      case ExportResolution.p1440:
        return '1440p';
      case ExportResolution.p2160:
        return '2160p (4K)';
    }
  }

  Future<void> _exportVideo() async {
    setState(() {
      _exportProgress = '0%';
    });

    // Simulate export progress
    for (int i = 0; i <= 100; i += 5) {
      await Future.delayed(const Duration(milliseconds: 300));
      if (i == 100) {
        setState(() {
          _exportProgress = 'Complete';
        });
      } else {
        setState(() {
          _exportProgress = '$i%';
        });
      }
    }

    // In a real app, this would involve actual video processing and export
  }

  void _shareVideo(String platform) {
    // Logic to share the video on the selected platform
    // In a real app, this would use platform-specific sharing APIs
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'Sharing on $platform is not implemented in this prototype',
        ),
      ),
    );
  }

  void _recordEdit(EditType type, dynamic previousValue, dynamic newValue) {
    _editHistory.add(
      EditAction(type: type, previousValue: previousValue, newValue: newValue),
    );
    _redoStack.clear(); // Clear redo stack when new edit is made
  }

  void _undo() {
    if (_editHistory.isNotEmpty) {
      EditAction action = _editHistory.removeLast();
      _redoStack.add(action);

      // Apply the undo based on action type
      switch (action.type) {
        case EditType.clipSplit:
          // Logic to undo clip split
          break;
        case EditType.clipDelete:
          // Logic to restore deleted clip
          break;
        case EditType.clipCrop:
          // Logic to undo crop adjustment
          _selectedStart = action.previousValue['start'] as double;
          _selectedEnd = action.previousValue['end'] as double;
          break;
        case EditType.clipRotate:
          // Logic to undo rotation
          _rotation = action.previousValue as double;
          break;
        case EditType.clipFlip:
          // Logic to undo flip
          break;
        case EditType.filterChange:
          // Logic to undo filter change
          _selectedFilter = action.previousValue as String;
          break;
        case EditType.effectChange:
          // Logic to undo effect change
          _enableGrain = action.previousValue['grain'] as bool;
          _enableVignette = action.previousValue['vignette'] as bool;
          _enableLomo = action.previousValue['lomo'] as bool;
          _enableSepia = action.previousValue['sepia'] as bool;
          break;
        case EditType.textAdd:
          // Logic to undo text addition
          if (_textOverlays.isNotEmpty) {
            _textOverlays.removeLast();
          }
          break;
        case EditType.textEdit:
          // Logic to undo text edit
          int index = action.previousValue['index'] as int;
          if (index < _textOverlays.length) {
            _textOverlays[index] = TextOverlay(
              index: index,
              text: action.previousValue['text'] as String,
              fontSize: action.previousValue['fontSize'] as double,
              color: action.previousValue['color'] as Color,
              align: action.previousValue['align'] as TextAlign,
              style: action.previousValue['style'] as FontStyle,
              weight: action.previousValue['weight'] as FontWeight,
            );
          }
          break;
        case EditType.textDelete:
          // Logic to undo text deletion
          if (action.previousValue is TextOverlay) {
            _textOverlays.add(action.previousValue as TextOverlay);
          }
          break;
        case EditType.transitionAdd:
          // Logic to undo transition addition
          if (_selectedTransitionType !=
              action.previousValue as TransitionType) {
            _selectedTransitionType = action.previousValue as TransitionType;
          }
          break;
        case EditType.transitionDelete:
          // Logic to undo transition deletion
          break;
        case EditType.audioAdjust:
          // Logic to undo audio adjustment
          _musicVolume = action.previousValue['musicVolume'] as double;
          _voiceVolume = action.previousValue['voiceVolume'] as double;
          _enableFadeIn = action.previousValue['fadeIn'] as bool;
          _enableFadeOut = action.previousValue['fadeOut'] as bool;
          _enableDucking = action.previousValue['ducking'] as bool;
          break;
      }

      setState(() {});
    }
  }

  void _redo() {
    if (_redoStack.isNotEmpty) {
      EditAction action = _redoStack.removeLast();
      _recordEdit(action.type, action.previousValue, action.newValue);

      // Apply the redo based on action type
      switch (action.type) {
        case EditType.clipSplit:
          // Logic to redo clip split
          break;
        case EditType.clipDelete:
          // Logic to redo clip deletion
          if (_mediaFiles.isNotEmpty) {
            _mediaFiles.removeLast();
          }
          break;
        case EditType.clipCrop:
          // Logic to redo crop adjustment
          _selectedStart = action.newValue['start'] as double;
          _selectedEnd = action.newValue['end'] as double;
          break;
        case EditType.clipRotate:
          // Logic to redo rotation
          _rotation = action.newValue as double;
          break;
        case EditType.clipFlip:
          // Logic to redo flip
          break;
        case EditType.filterChange:
          // Logic to redo filter change
          _selectedFilter = action.newValue as String;
          break;
        case EditType.effectChange:
          // Logic to redo effect change
          _enableGrain = action.newValue['grain'] as bool;
          _enableVignette = action.newValue['vignette'] as bool;
          _enableLomo = action.newValue['lomo'] as bool;
          _enableSepia = action.newValue['sepia'] as bool;
          break;
        case EditType.textAdd:
          // Logic to redo text addition
          if (action.newValue is TextOverlay) {
            _textOverlays.add(action.newValue as TextOverlay);
          }
          break;
        case EditType.textEdit:
          // Logic to redo text edit
          int index = action.newValue['index'] as int;
          if (index < _textOverlays.length) {
            _textOverlays[index] = TextOverlay(
              index: index,
              text: action.newValue['text'] as String,
              fontSize: action.newValue['fontSize'] as double,
              color: action.newValue['color'] as Color,
              align: action.newValue['align'] as TextAlign,
              style: action.newValue['style'] as FontStyle,
              weight: action.newValue['weight'] as FontWeight,
            );
          }
          break;
        case EditType.textDelete:
          // Logic to redo text deletion
          if (_textOverlays.isNotEmpty) {
            _textOverlays.removeLast();
          }
          break;
        case EditType.transitionAdd:
          // Logic to redo transition addition
          _selectedTransitionType = action.newValue as TransitionType;
          break;
        case EditType.transitionDelete:
          // Logic to redo transition deletion
          break;
        case EditType.audioAdjust:
          // Logic to redo audio adjustment
          _musicVolume = action.newValue['musicVolume'] as double;
          _voiceVolume = action.newValue['voiceVolume'] as double;
          _enableFadeIn = action.newValue['fadeIn'] as bool;
          _enableFadeOut = action.newValue['fadeOut'] as bool;
          _enableDucking = action.newValue['ducking'] as bool;
          break;
      }

      setState(() {});
    }
  }

  void _addUndoHistory(EditType type, dynamic previousValue, dynamic newValue) {
    _recordEdit(type, previousValue, newValue);
  }

  Widget _buildPiPControls() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.grey[900],
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Picture in Picture (PiP)',
                style: TextStyle(color: Colors.white, fontSize: 16),
              ),
              IconButton(
                icon: Icon(Icons.close, color: Colors.white),
                onPressed: () {
                  setState(() {
                    _showPiPControls = false;
                  });
                },
              ),
            ],
          ),
          // PiP toggle
          Row(
            children: [
              Text('Enable PiP', style: TextStyle(color: Colors.white)),
              const SizedBox(width: 16),
              Switch(
                value: _enablePiP,
                onChanged: (value) {
                  setState(() {
                    _enablePiP = value;
                    _applyPiPSettings();
                  });
                },
                activeColor: Colors.orange,
              ),
            ],
          ),
          // PiP position
          Text(
            'Position & Size',
            style: TextStyle(color: Colors.white, fontSize: 14),
          ),
          const SizedBox(height: 8),
          Slider(
            value: _pipPosition.left,
            min: 0,
            max: MediaQuery.of(context).size.width - _pipPosition.width,
            onChanged: (value) {
              setState(() {
                _pipPosition = Rect.fromLTWH(
                  value,
                  _pipPosition.top,
                  _pipPosition.width,
                  _pipPosition.height,
                );
                _applyPiPSettings();
              });
            },
            activeColor: Colors.orange,
          ),
          Slider(
            value: _pipPosition.top,
            min: 0,
            max: MediaQuery.of(context).size.height - _pipPosition.height,
            onChanged: (value) {
              setState(() {
                _pipPosition = Rect.fromLTWH(
                  _pipPosition.left,
                  value,
                  _pipPosition.width,
                  _pipPosition.height,
                );
                _applyPiPSettings();
              });
            },
            activeColor: Colors.orange,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildPiPSizeButton(120, 90),
              _buildPiPSizeButton(160, 120),
              _buildPiPSizeButton(200, 150),
            ],
          ),
          const SizedBox(height: 16),
          // PiP appearance
          Text(
            'Appearance',
            style: TextStyle(color: Colors.white, fontSize: 14),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Text(
                'Opacity: ${(_pipOpacity * 100).toInt()}%',
                style: TextStyle(color: Colors.white),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Slider(
                  value: _pipOpacity,
                  min: 0.3,
                  max: 1.0,
                  onChanged: (value) {
                    setState(() {
                      _pipOpacity = value;
                      _applyPiPSettings();
                    });
                  },
                  activeColor: Colors.orange,
                ),
              ),
            ],
          ),
          Row(
            children: [
              Text(
                'Corner Radius: ${_pipCornerRadius.toInt()}',
                style: TextStyle(color: Colors.white),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Slider(
                  value: _pipCornerRadius,
                  min: 0,
                  max: 20,
                  divisions: 20,
                  label: '${_pipCornerRadius.toInt()}',
                  onChanged: (value) {
                    setState(() {
                      _pipCornerRadius = value;
                      _applyPiPSettings();
                    });
                  },
                  activeColor: Colors.orange,
                ),
              ),
            ],
          ),
          // Apply PiP button
          ElevatedButton.icon(
            onPressed:
                _enablePiP
                    ? null
                    : () {
                      setState(() {
                        _enablePiP = true;
                        _applyPiPSettings();
                      });
                    },
            icon: Icon(Icons.video_call),
            label: Text(_enablePiP ? 'PiP Enabled' : 'Enable PiP'),
            style: ElevatedButton.styleFrom(
              backgroundColor: _enablePiP ? Colors.green : Colors.orange,
              foregroundColor: Colors.black,
              minimumSize: const Size(double.infinity, 50),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPiPSizeButton(double width, double height) {
    return TextButton(
      onPressed: () {
        setState(() {
          _pipPosition = Rect.fromLTWH(
            _pipPosition.left,
            _pipPosition.top,
            width,
            height,
          );
          _applyPiPSettings();
        });
      },
      child: Text(
        '${width.toInt()}x${height.toInt()}',
        style: TextStyle(color: Colors.black),
      ),
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.orange,
        foregroundColor: Colors.black,
      ),
    );
  }

  void _applyPiPSettings() {
    // Logic to apply PiP settings to the video
    // This would typically involve using a video processing library

    // Record edit for undo/redo
    _addUndoHistory(
      EditType.transitionAdd,
      {
        'enablePiP': !_enablePiP,
        'position': _pipPosition,
        'opacity': _pipOpacity,
        'cornerRadius': _pipCornerRadius,
      },
      {
        'enablePiP': _enablePiP,
        'position': _pipPosition,
        'opacity': _pipOpacity,
        'cornerRadius': _pipCornerRadius,
      },
    );
  }

  void _showPiPControls() {
    setState(() {
      _showPiPControls = true;
    });
  }

  Widget _buildStoryFormatPanel() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.grey[900],
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('Story Format', style: TextStyle(color: Colors.white, fontSize: 16)),
              IconButton(
                icon: Icon(Icons.close, color: Colors.white),
                onPressed: () {
                  setState(() {
                    _showStoryFormatPanel = false;
                  });
                },
              ),
            ],
          ),
          // Story format selection
          Text('Select Story Format', style: TextStyle(color: Colors.white, fontSize: 14)),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildStoryFormatButton(StoryFormat.instagram, 'Instagram', Icons.photo_camera),
              _buildStoryFormatButton(StoryFormat.snapchat, 'Snapchat', Icons.camera_alt),
              _buildStoryFormatButton(StoryFormat.whatsapp, 'WhatsApp', Icons.chat),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildStoryFormatButton(StoryFormat.tiktok, 'TikTok', Icons.music_note),
              _buildStoryFormatButton(StoryFormat.twitter, 'Twitter', Icons.edit),n
              _buildStoryFormatButton(StoryFormat.facebook, 'Facebook', Icons.video_call),
            ],
          ),
          const SizedBox(height: 16),
          // Aspect ratio display
          Row(
            children: [
              Text('Aspect Ratio: ${_getAspectRatioDescription(_selectedStoryFormat)}',
                  style: TextStyle(color: Colors.white)),
              const SizedBox(width: 16),
              Expanded(
                child: LinearProgressIndicator(
                  value: _getAspectRatioProgress(_selectedStoryFormat),
                  color: Colors.orange,
                  backgroundColor: Colors.grey[700],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          // Story format preview
          Container(
            margin: const EdgeInsets.symmetric(vertical: 16),
            height: 300,
            decoration: BoxDecoration(
              color: Colors.black,
              borderRadius: BorderRadius.circular(8),
            ),
            child: _buildStoryFormatPreview(),
          ),
          // Apply story format button
          ElevatedButton.icon(
            onPressed: _enableStoryFormat ? null : () {
              setState(() {
                _enableStoryFormat = true;
                _applyStoryFormat();
              });
            },
            icon: Icon(Icons.format_paint),
            label: Text(_enableStoryFormat ? 'Story Format Applied' : 'Apply Story Format'),
            style: ElevatedButton.styleFrom(
              backgroundColor: _enableStoryFormat ? Colors.green : Colors.orange,
              foregroundColor: Colors.black,
              minimumSize: const Size(double.infinity, 50),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStoryFormatButton(StoryFormat format, String name, IconData icon) {
    return Expanded(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 4.0),
        child: ElevatedButton.icon(
          onPressed: () {
            setState(() {
              _selectedStoryFormat = format;
            });
          },
          icon: Icon(icon, color: Colors.white),
          label: Text(name),
          style: ElevatedButton.styleFrom(
            backgroundColor: _selectedStoryFormat == format ? Colors.orange : Colors.grey[800],
            foregroundColor: Colors.white,
          ),
        ),
      ),
    );
  }

  String _getAspectRatioDescription(StoryFormat format) {
    switch (format) {
      case StoryFormat.instagram:
      case StoryFormat.snapchat:
      case StoryFormat.whatsapp:
        return '9:16 (Vertical)';
      case StoryFormat.tiktok:
      case StoryFormat.twitter:
      case StoryFormat.facebook:
        return '1:1 (Square)';
    }
  }

  double _getAspectRatioProgress(StoryFormat format) {
    double value = 0;
    switch (format) {
      case StoryFormat.instagram:
      case StoryFormat.snapchat:
      case StoryFormat.whatsapp:
        value = 0.95; // High priority formats
        break;
      case StoryFormat.tiktok:
      case StoryFormat.twitter:
      case StoryFormat.facebook:
        value = 0.7; // Medium priority formats
        break;
    }
    return value;
  }

  Widget _buildStoryFormatPreview() {
    final aspectRatio = _getAspectRatioDescription(_selectedStoryFormat);
    final isVertical = aspectRatio.contains('9:16');
    
    return LayoutBuilder(
      builder: (context, constraints) {
        final containerWidth = constraints.maxWidth;
        final containerHeight = isVertical ? containerWidth * 16 / 9 : containerWidth;
        
        return Stack(
          children: [
            // Story format frame
            Positioned.fill(
              child: Container(
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.orange, width: 4),
                  borderRadius: BorderRadius.circular(isVertical ? 0 : 16),
                ),
                child: Center(
                  child: Text(
                    aspectRatio,
                    style: TextStyle(color: Colors.orange, fontSize: 16),
                  ),
                ),
              ),
            ),
            // Video preview within story format
            Positioned(
              left: isVertical ? (containerWidth - (isVertical ? containerHeight * 9 / 16 : containerWidth) / 2) : 0,
              top: isVertical ? 0 : (containerHeight - (isVertical ? containerHeight : containerWidth * 9 / 16) / 2),
              width: isVertical ? containerHeight * 9 / 16 : containerWidth,
              height: isVertical ? containerHeight : containerWidth * 9 / 16,
              child: AspectRatio(
                aspectRatio: 16 / 9,
                child: Container(
                  color: Colors.black,
                  child: Center(
                    child: _controller.value.isInitialized
                        ? AspectRatio(
                            aspectRatio: _controller.value.aspectRatio,
                            child: VideoPlayer(_controller),
                          )
                        : const Text('Video Preview'),
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  void _applyStoryFormat() {
    // Logic to apply the selected story format to the video
    // This would typically involve cropping and repositioning the video
    
    // Record edit for undo/redo
    _addUndoHistory(
      EditType.transitionAdd,
      {'storyFormat': !_enableStoryFormat},
      {'storyFormat': _enableStoryFormat},
    );
  }

  void _showStoryFormatPanel() {
    setState(() {
      _showStoryFormatPanel = true;
    });
  }
}

enum EditType {
  clipSplit,
  clipDelete,
  clipCrop,
  clipRotate,
  clipFlip,
  filterChange,
  effectChange,
  textAdd,
  textEdit,
  textDelete,
  transitionAdd,
  transitionDelete,
  audioAdjust,
}

class AudioTrack {
  final int id;
  String name;
  double volume;
  bool mute;
  bool solo;
  Color color;

  AudioTrack({
    required this.id,
    required this.name,
    required this.volume,
    required this.mute,
    required this.solo,
    required this.color,
  });

  Map<String, dynamic> toJson() => {
    'id': id,
    'name': name,
    'volume': volume,
    'mute': mute,
    'solo': solo,
    'color': '#${color.value.toRadixString(16)}',
  };
}

class TextOverlay {
  final int index;
  final String text;
  final double fontSize;
  final Color color;
  final TextAlign align;
  final FontStyle style;
  final FontWeight weight;

  TextOverlay({
    required this.index,
    required this.text,
    required this.fontSize,
    required this.color,
    required this.align,
    required this.style,
    required this.weight,
  });

  Map<String, dynamic> toJson() => {
    'index': index,
    'text': text,
    'fontSize': fontSize,
    'color': '#${color.value.toRadixString(16)}',
    'align': align.toString(),
    'style': style.toString(),
    'weight': weight.toString(),
  };
}

class Transition {
  final TransitionType type;
  final String name;
  final IconData icon;

  Transition({required TransitionType type})
    : type = type,
      name = _getTransitionName(type),
      icon = _getTransitionIcon(type);

  static IconData _getTransitionIcon(TransitionType type) {
    switch (type) {
      case TransitionType.fade:
        return Icons.opacity;
      case TransitionType.slide:
        return Icons.arrow_right_alt;
      case TransitionType.zoom:
        return Icons.zoom_out;
      case TransitionType.rotate:
        return Icons.rotate_right;
      case TransitionType.flip:
        return Icons.flip;
      case TransitionType.crossFade:
        return Icons.compare_arrows;
      case TransitionType.dissolve:
        return Icons.blur_on;
      case TransitionType.wipe:
        return Icons.arrow_right_alt;
      case TransitionType.push:
        return Icons.arrow_forward;
      case TransitionType.cube:
        return Icons.layers;
      case TransitionType.door:
        return Icons.door_back_door;
    }
  }

  static String _getTransitionName(TransitionType type) {
    switch (type) {
      case TransitionType.fade:
        return 'Fade';
      case TransitionType.slide:
        return 'Slide';
      case TransitionType.zoom:
        return 'Zoom';
      case TransitionType.rotate:
        return 'Rotate';
      case TransitionType.flip:
        return 'Flip';
      case TransitionType.crossFade:
        return 'Cross Fade';
      case TransitionType.dissolve:
        return 'Dissolve';
      case TransitionType.wipe:
        return 'Wipe';
      case TransitionType.push:
        return 'Push';
      case TransitionType.cube:
        return 'Cube';
      case TransitionType.door:
        return 'Door';
    }
  }
}

class Track extends StatefulWidget {
  final String type;
  final Color? color;
  final double duration;
  final bool enableTransitions; // Allow transitions between clips

  const Track({
    super.key,
    required this.type,
    required this.color,
    required this.duration,
    this.enableTransitions = false, // Default to false
  });

  @override
  State<Track> createState() => _TrackState();
}

class _TrackState extends State<Track> {
  // Selected clip for editing
  double _start = 0;
  double _end = 5; // Default clip duration
  List<Clip> _clips = [];

  @override
  void initState() {
    super.initState();
    // Initialize with one clip
    _clips.add(Clip(start: 0, end: 5, color: widget.color));
  }

  void _addClip() {
    // Add a new clip after the current one
    double newStart = _end + 1;
    double newEnd = newStart + 5;
    if (newEnd > widget.duration) newEnd = widget.duration;

    setState(() {
      _clips.add(Clip(start: newStart, end: newEnd, color: widget.color));
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 80,
      padding: const EdgeInsets.all(8.0),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '${widget.type[0].toUpperCase()}${widget.type.substring(1)} Track',
                style: const TextStyle(color: Colors.white),
              ),
              if (widget.enableTransitions)
                IconButton(
                  icon: const Icon(Icons.swap_horiz, color: Colors.white),
                  onPressed: _addClip,
                  tooltip: 'Add clip',
                ),
            ],
          ),
          Expanded(
            child: Container(
              color: widget.color?.withOpacity(0.3),
              child: Stack(
                children: [
                  // Time ruler
                  const TimeRuler(),
                  // Clip
                  Positioned(
                    left:
                        (_start / widget.duration) *
                        MediaQuery.of(context).size.width,
                    width:
                        ((_end - _start) / widget.duration) *
                        MediaQuery.of(context).size.width,
                    top: 0,
                    bottom: 0,
                    child: GestureDetector(
                      onHorizontalDragUpdate: (details) {
                        // Handle clip movement
                      },
                      onTap: () {
                        // Show editing tools when clip is tapped
                        if (widget.type == 'video') {
                          final VideoEditorHomeState? state =
                              context
                                  .findAncestorStateOfType<
                                    VideoEditorHomeState
                                  >();
                          if (state != null) {
                            state.showEditingTools();
                          }
                        }
                      },
                      child: Container(
                        color: widget.color,
                        child: Center(child: Text('${_end - _start}s')),
                      ),
                    ),
                  ),
                  // Transition indicators if enabled
                  if (widget.enableTransitions)
                    ..._clips.asMap().entries.map((entry) {
                      int index = entry.key;
                      Clip clip = entry.value;
                      if (index > 0) {
                        double prevEnd = _clips[index - 1].end;
                        double transitionStart = prevEnd;
                        double transitionWidth =
                            10.0; // Fixed width for transition indicator

                        return Positioned(
                          left:
                              (transitionStart / widget.duration) *
                                  MediaQuery.of(context).size.width -
                              transitionWidth / 2,
                          width: transitionWidth,
                          top: 0,
                          bottom: 0,
                          child: Container(
                            color: Colors.white.withOpacity(0.5),
                            child: const Center(
                              child: Icon(
                                Icons.crop_din,
                                size: 16,
                                color: Colors.black,
                              ),
                            ),
                          ),
                        );
                      }
                      return const SizedBox.shrink();
                    }).toList(),
                ],
              ),
            ),
          ),
          if (_showEditingTools) _buildEditingTools(),
        ],
      ),
    );
  }
}

class Clip {
  double start;
  double end;
  final Color? color;

  Clip({required this.start, required this.end, this.color});
}

class TimeRuler extends StatelessWidget {
  const TimeRuler({super.key});

  @override
  Widget build(BuildContext context) {
    return Row(
      children: List.generate(
        // Generate time markers every second
        60, // Adjust based on video duration
        (i) {
          final double position = i * (MediaQuery.of(context).size.width / 60);
          final bool isMajorTick = i % 5 == 0;

          return SizedBox(
            width: isMajorTick ? 40 : 20,
            child: Column(
              children: [
                Container(
                  height: isMajorTick ? 20 : 10,
                  width: 1,
                  color: Colors.white,
                ),
                if (isMajorTick)
                  Text(
                    '$i',
                    style: const TextStyle(color: Colors.white, fontSize: 10),
                  ),
              ],
            ),
          );
        },
      ),
    );
  }
}

enum ExportFormat { mp4, mov, webm, gif }

enum ExportResolution { p480, p720, p1080, p1440, p2160 }

enum StoryFormat {
  instagram,
  snapchat,
  whatsapp,
  tiktok,
  twitter,
  facebook,
}
