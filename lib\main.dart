import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';
import 'package:image_picker/image_picker.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

// Import models
import 'models/project.dart';

// Import services
import 'services/project_service.dart';

void main() {
  runApp(const VideoEditorApp());
}

class VideoEditorApp extends StatelessWidget {
  const VideoEditorApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'MontagePro',
      theme: ThemeData(
        primarySwatch: Colors.orange,
        brightness: Brightness.dark,
      ),
      home: const ProjectSelectionScreen(),
    );
  }
}

class ProjectSelectionScreen extends StatefulWidget {
  const ProjectSelectionScreen({super.key});

  @override
  State<ProjectSelectionScreen> createState() => _ProjectSelectionScreenState();
}

class _ProjectSelectionScreenState extends State<ProjectSelectionScreen> {
  final ProjectService _projectService = ProjectService();
  List<Project> _projects = [];

  @override
  void initState() {
    super.initState();
    _loadProjects();
  }

  Future<void> _loadProjects() async {
    try {
      final projects = await _projectService.loadProjects();
      setState(() {
        _projects = projects;
      });
    } catch (e) {
      // Handle error
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error loading projects: $e')));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('MontagePro'),
        backgroundColor: Colors.black,
      ),
      backgroundColor: Colors.black,
      body: Column(
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(16),
            child: const Text(
              'Select a Project',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
          ),
          // Project list
          Expanded(
            child: ListView.builder(
              itemCount: _projects.length,
              itemBuilder: (context, index) {
                final project = _projects[index];
                return Card(
                  margin: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ),
                  color: Colors.grey[800],
                  child: ListTile(
                    title: Text(
                      project.name,
                      style: const TextStyle(color: Colors.white),
                    ),
                    subtitle: Text(
                      'Created: ${project.createdAt.toString().split(' ')[0]}',
                      style: const TextStyle(color: Colors.grey),
                    ),
                    trailing: const Icon(
                      Icons.arrow_forward,
                      color: Colors.orange,
                    ),
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder:
                              (context) => VideoEditorHome(project: project),
                        ),
                      );
                    },
                  ),
                );
              },
            ),
          ),
          // Create new project button
          Padding(
            padding: const EdgeInsets.all(16),
            child: ElevatedButton.icon(
              onPressed: () async {
                final navigator = Navigator.of(context);
                final newProject = await _projectService.createProject(
                  'New Project',
                );
                if (mounted) {
                  navigator.push(
                    MaterialPageRoute(
                      builder:
                          (context) => VideoEditorHome(project: newProject),
                    ),
                  );
                }
              },
              icon: const Icon(Icons.add),
              label: const Text('Create New Project'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.black,
                minimumSize: const Size(double.infinity, 50),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class VideoEditorHome extends StatefulWidget {
  final Project project;

  const VideoEditorHome({super.key, required this.project});

  @override
  State<VideoEditorHome> createState() => _VideoEditorHomeState();
}

class _VideoEditorHomeState extends State<VideoEditorHome> {
  late VideoPlayerController _controller;
  final ImagePicker _picker = ImagePicker();
  final ProjectService _projectService = ProjectService();
  late Project _currentProject;
  final List<XFile> _mediaFiles = [];

  @override
  void initState() {
    super.initState();
    _currentProject = widget.project;
    _initializeVideo();
  }

  void _initializeVideo() {
    // Initialize with a default video or the first clip
    if (_currentProject.clips.isNotEmpty) {
      _controller = VideoPlayerController.networkUrl(
          Uri.parse(
            'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4',
          ),
        )
        ..initialize().then((_) {
          setState(() {});
        });
    } else {
      _controller = VideoPlayerController.networkUrl(
          Uri.parse(
            'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4',
          ),
        )
        ..initialize().then((_) {
          setState(() {});
        });
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_currentProject.name),
        backgroundColor: Colors.black,
        actions: [
          IconButton(
            icon: const Icon(FontAwesomeIcons.floppyDisk),
            onPressed: () {
              _saveProject();
            },
            tooltip: 'Save Project',
          ),
          IconButton(
            icon: const Icon(FontAwesomeIcons.shareFromSquare),
            onPressed: () {
              _exportProject();
            },
            tooltip: 'Export & Share',
          ),
        ],
      ),
      backgroundColor: Colors.black,
      body: Column(
        children: [
          // Video preview area
          Expanded(
            flex: 3,
            child: Container(
              color: Colors.black,
              child: Center(
                child:
                    _controller.value.isInitialized
                        ? AspectRatio(
                          aspectRatio: _controller.value.aspectRatio,
                          child: VideoPlayer(_controller),
                        )
                        : const CircularProgressIndicator(color: Colors.orange),
              ),
            ),
          ),
          // Controls
          Container(
            padding: const EdgeInsets.all(16),
            color: Colors.grey[900],
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    _controller.value.isPlaying
                        ? Icons.pause
                        : Icons.play_arrow,
                    color: Colors.orange,
                  ),
                  onPressed: () {
                    setState(() {
                      _controller.value.isPlaying
                          ? _controller.pause()
                          : _controller.play();
                    });
                  },
                ),
                IconButton(
                  icon: const Icon(Icons.stop, color: Colors.orange),
                  onPressed: () {
                    _controller.seekTo(Duration.zero);
                    _controller.pause();
                  },
                ),
                IconButton(
                  icon: const Icon(Icons.add_a_photo, color: Colors.orange),
                  onPressed: _addMedia,
                ),
              ],
            ),
          ),
          // Timeline area
          Expanded(
            flex: 1,
            child: Container(
              color: Colors.grey[800],
              child: const Center(
                child: Text(
                  'Timeline',
                  style: TextStyle(color: Colors.white, fontSize: 18),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _addMedia() async {
    try {
      final XFile? file = await _picker.pickVideo(source: ImageSource.gallery);
      if (file != null) {
        setState(() {
          _mediaFiles.add(file);
        });
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Media added successfully')),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error adding media: $e')));
      }
    }
  }

  Future<void> _saveProject() async {
    try {
      await _projectService.saveProject(_currentProject);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Project saved successfully')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error saving project: $e')));
      }
    }
  }

  Future<void> _exportProject() async {
    try {
      await _projectService.exportProject(_currentProject);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Project exported successfully')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error exporting project: $e')));
      }
    }
  }
}
